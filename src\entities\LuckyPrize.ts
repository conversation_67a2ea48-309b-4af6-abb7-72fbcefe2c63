import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm'

@Entity('prizes')
export class LuckyPrize {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 765 })
  name: string

  @Column('varchar', { name: 'image', length: 765, nullable: true })
  image: string

  @Column('double', { name: 'winrate' })
  winrate: number

  @Column('int', { name: 'quantity' })
  quantity: number

  @Column('tinyint', { name: 'active' })
  active: number

  @Column('int', { name: 'display_order' })
  displayOrder: number

  @Column('text', { name: 'description', nullable: true })
  description: string

  @Column('char', { name: 'type', length: 24 })
  type: string

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null

  @Column('int', { name: 'point' })
  point: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'biz_storage_id', nullable: true })
  bizStorageId: number | null

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('varchar', { name: 'image_slug', length: 128, nullable: true })
  imageSlug: string
} 